# User Story 3655: Add Optional Procedure Details to Cancer Screening SMS API

## Overview

This implementation adds two new optional fields to the Cancer Screening SMS API:
- `procedureName` (string, max 250 characters)
- `procedureCode` (string, max 100 characters)

## Changes Made

### 1. Updated Request Models
- **NewAppointmentRequest.cs**: Added ProcedureName and ProcedureCode properties
- **RescheduleAppointmentRequest.cs**: Added ProcedureName and ProcedureCode properties

### 2. Updated Validators
- **NewAppointmentRequestValidator.cs**: Added validation rules for optional fields
  - ProcedureName: Max 250 characters, error message: "ProcedureName cannot exceed 200 characters."
  - ProcedureCode: Max 100 characters, error message: "ProcedureCode cannot exceed 50 characters."
- **RescheduleAppointmentValidator.cs**: Added validation rules for optional fields
  - ProcedureName: Max 250 characters, error message: "ProcedureName cannot exceed 200 characters."
  - ProcedureCode: Max 100 characters, error message: "ProcedureCode cannot exceed 50 characters."

### 3. Updated Handlers
- **NewAppointmentHandler.cs**: Added logic to store optional procedure details
  - Added code to store ProcedureName in phcc_procedurename field
  - Added code to store ProcedureCode in phcc_procedurecode field
- **RescheduleAppointmentHandler.cs**: Added logic to store optional procedure details
  - Added code to store ProcedureName in phcc_procedurename field
  - Added code to store ProcedureCode in phcc_procedurecode field

## API Usage Examples

### New Appointment with Procedure Details
```json
POST /api/appointment/new
{
  "appointmentIdentifier": "12345678",
  "appointmentType": "Breast Screening",
  "procedureName": "Screening Mammogram",
  "procedureCode": "77067",
  "appointmentLocation": "RAK",
  "appointmentLocationDescription": "Rawdat Al Khail",
  "appointmentDateTime": "2025-04-30T10:00:00",
  "appointmentArriveMinutesBefore": 30,
  "customIdentifier": "**********",
  "subjectIdentifier": "**********",
  "recipientFirstName": "AAAAAAAAAAA",
  "recipientLastName": "bbbbbbbb",
  "recipientPhone": "12345678",
  "recipientEmail": "<EMAIL>",
  "sendDateTime": "2025-04-28T12:48:50",
  "reminderSendHoursBefore": 24,
  "reminderType": "Screening",
  "recipientNationality": "undefined"
}
```

### New Appointment without Procedure Details (Backward Compatible)
```json
POST /api/appointment/new
{
  "appointmentIdentifier": "12345679",
  "appointmentType": "Breast Screening",
  "appointmentLocation": "RAK",
  "appointmentLocationDescription": "Rawdat Al Khail",
  "appointmentDateTime": "2025-04-30T10:00:00",
  "appointmentArriveMinutesBefore": 30,
  "customIdentifier": "HC00000002",
  "subjectIdentifier": "HC00000002",
  "recipientFirstName": "AAAAAAAAAAA",
  "recipientLastName": "bbbbbbbb",
  "recipientPhone": "12345678",
  "recipientEmail": "<EMAIL>",
  "sendDateTime": "2025-04-28T12:48:50",
  "reminderSendHoursBefore": 24,
  "reminderType": "Screening",
  "recipientNationality": "undefined"
}
```

## Validation Rules

### ProcedureName
- Type: String
- Required: No (optional)
- Max Length: 250 characters
- Error Message: "ProcedureName cannot exceed 200 characters."

### ProcedureCode
- Type: String
- Required: No (optional)
- Max Length: 100 characters
- Error Message: "ProcedureCode cannot exceed 50 characters."

## Database Fields

The following fields are stored in the database when provided:
- `phcc_procedurename`: Stores the procedure name
- `phcc_procedurecode`: Stores the procedure code

## Testing Scenarios

### 1. Valid Requests
- ✅ Request with both procedure fields provided
- ✅ Request with only procedure name provided
- ✅ Request with only procedure code provided
- ✅ Request with neither procedure field provided (backward compatibility)

### 2. Validation Errors
- ❌ Request with procedure name exceeding 250 characters
- ❌ Request with procedure code exceeding 100 characters

### 3. Expected Responses
- Success: 201 Created with appointment details
- Validation Error: 400 Bad Request with validation error messages

## Backward Compatibility
- ✅ Fully backward compatible - existing integrations will continue to work without any changes as both new fields are optional.

## Testing
A PowerShell test script (`test-procedure-fields.ps1`) has been created to test the implementation. The script tests all the scenarios mentioned above.

## Future Enhancements
The procedureName field can be incorporated into SMS message templates to provide more specific appointment reminders to patients.
