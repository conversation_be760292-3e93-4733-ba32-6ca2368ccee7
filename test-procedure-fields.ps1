# Test script for User Story 3655: Add Optional Procedure Details to Cancer Screening SMS API
# This script tests the new optional fields (procedureName and procedureCode) in the API

# API Base URL - Change this to match your environment
$baseUrl = "https://localhost:5001"

# Authentication headers
$headers = @{
    "X-Client-Id" = "test"
    "X-Secret-Key" = "test"
    "Content-Type" = "application/json"
}

# Test Case 1: New Appointment with both procedure fields
Write-Host "Test Case 1: New Appointment with both procedure fields" -ForegroundColor Green
$body1 = @{
    appointmentIdentifier = "TEST001"
    appointmentType = "Breast Screening"
    procedureName = "Screening Mammogram"
    procedureCode = "77067"
    appointmentLocation = "RAK"
    appointmentLocationDescription = "Rawdat Al Khail"
    appointmentDateTime = (Get-Date).AddDays(30).ToString("yyyy-MM-ddTHH:mm:ss")
    appointmentArriveMinutesBefore = 30
    customIdentifier = "HC00000001"
    subjectIdentifier = "HC00000001"
    recipientFirstName = "AAAAAAAAAAA"
    recipientLastName = "bbbbbbbb"
    recipientPhone = "12345678"
    recipientEmail = "<EMAIL>"
    sendDateTime = (Get-Date).AddDays(28).ToString("yyyy-MM-ddTHH:mm:ss")
    reminderSendHoursBefore = 24
    reminderType = "Screening"
    recipientNationality = "undefined"
} | ConvertTo-Json

try {
    $response1 = Invoke-RestMethod -Uri "$baseUrl/api/appointment/new" -Method POST -Headers $headers -Body $body1 -SkipCertificateCheck
    Write-Host "Success: Created appointment with both procedure fields" -ForegroundColor Green
    Write-Host "Response: $($response1 | ConvertTo-Json)" -ForegroundColor Gray
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
    Write-Host "Response: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# Test Case 2: New Appointment with only procedure name
Write-Host "`nTest Case 2: New Appointment with only procedure name" -ForegroundColor Green
$body2 = @{
    appointmentIdentifier = "TEST002"
    appointmentType = "Breast Screening"
    procedureName = "Screening Mammogram"
    appointmentLocation = "RAK"
    appointmentLocationDescription = "Rawdat Al Khail"
    appointmentDateTime = (Get-Date).AddDays(30).ToString("yyyy-MM-ddTHH:mm:ss")
    appointmentArriveMinutesBefore = 30
    customIdentifier = "HC00000002"
    subjectIdentifier = "HC00000002"
    recipientFirstName = "AAAAAAAAAAA"
    recipientLastName = "bbbbbbbb"
    recipientPhone = "12345678"
    recipientEmail = "<EMAIL>"
    sendDateTime = (Get-Date).AddDays(28).ToString("yyyy-MM-ddTHH:mm:ss")
    reminderSendHoursBefore = 24
    reminderType = "Screening"
    recipientNationality = "undefined"
} | ConvertTo-Json

try {
    $response2 = Invoke-RestMethod -Uri "$baseUrl/api/appointment/new" -Method POST -Headers $headers -Body $body2 -SkipCertificateCheck
    Write-Host "Success: Created appointment with only procedure name" -ForegroundColor Green
    Write-Host "Response: $($response2 | ConvertTo-Json)" -ForegroundColor Gray
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
    Write-Host "Response: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# Test Case 3: New Appointment with only procedure code
Write-Host "`nTest Case 3: New Appointment with only procedure code" -ForegroundColor Green
$body3 = @{
    appointmentIdentifier = "TEST003"
    appointmentType = "Breast Screening"
    procedureCode = "77067"
    appointmentLocation = "RAK"
    appointmentLocationDescription = "Rawdat Al Khail"
    appointmentDateTime = (Get-Date).AddDays(30).ToString("yyyy-MM-ddTHH:mm:ss")
    appointmentArriveMinutesBefore = 30
    customIdentifier = "HC00000003"
    subjectIdentifier = "HC00000003"
    recipientFirstName = "AAAAAAAAAAA"
    recipientLastName = "bbbbbbbb"
    recipientPhone = "12345678"
    recipientEmail = "<EMAIL>"
    sendDateTime = (Get-Date).AddDays(28).ToString("yyyy-MM-ddTHH:mm:ss")
    reminderSendHoursBefore = 24
    reminderType = "Screening"
    recipientNationality = "undefined"
} | ConvertTo-Json

try {
    $response3 = Invoke-RestMethod -Uri "$baseUrl/api/appointment/new" -Method POST -Headers $headers -Body $body3 -SkipCertificateCheck
    Write-Host "Success: Created appointment with only procedure code" -ForegroundColor Green
    Write-Host "Response: $($response3 | ConvertTo-Json)" -ForegroundColor Gray
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
    Write-Host "Response: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# Test Case 4: New Appointment without procedure fields (backward compatibility)
Write-Host "`nTest Case 4: New Appointment without procedure fields (backward compatibility)" -ForegroundColor Green
$body4 = @{
    appointmentIdentifier = "TEST004"
    appointmentType = "Breast Screening"
    appointmentLocation = "RAK"
    appointmentLocationDescription = "Rawdat Al Khail"
    appointmentDateTime = (Get-Date).AddDays(30).ToString("yyyy-MM-ddTHH:mm:ss")
    appointmentArriveMinutesBefore = 30
    customIdentifier = "HC00000004"
    subjectIdentifier = "HC00000004"
    recipientFirstName = "AAAAAAAAAAA"
    recipientLastName = "bbbbbbbb"
    recipientPhone = "12345678"
    recipientEmail = "<EMAIL>"
    sendDateTime = (Get-Date).AddDays(28).ToString("yyyy-MM-ddTHH:mm:ss")
    reminderSendHoursBefore = 24
    reminderType = "Screening"
    recipientNationality = "undefined"
} | ConvertTo-Json

try {
    $response4 = Invoke-RestMethod -Uri "$baseUrl/api/appointment/new" -Method POST -Headers $headers -Body $body4 -SkipCertificateCheck
    Write-Host "Success: Created appointment without procedure fields" -ForegroundColor Green
    Write-Host "Response: $($response4 | ConvertTo-Json)" -ForegroundColor Gray
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
    Write-Host "Response: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# Test Case 5: Validation Error - ProcedureName too long
Write-Host "`nTest Case 5: Validation Error - ProcedureName too long" -ForegroundColor Green
$body5 = @{
    appointmentIdentifier = "TEST005"
    appointmentType = "Breast Screening"
    procedureName = "A" * 251  # 251 characters (exceeds 250 limit)
    appointmentLocation = "RAK"
    appointmentLocationDescription = "Rawdat Al Khail"
    appointmentDateTime = (Get-Date).AddDays(30).ToString("yyyy-MM-ddTHH:mm:ss")
    appointmentArriveMinutesBefore = 30
    customIdentifier = "HC00000005"
    subjectIdentifier = "HC00000005"
    recipientFirstName = "AAAAAAAAAAA"
    recipientLastName = "bbbbbbbb"
    recipientPhone = "12345678"
    recipientEmail = "<EMAIL>"
    sendDateTime = (Get-Date).AddDays(28).ToString("yyyy-MM-ddTHH:mm:ss")
    reminderSendHoursBefore = 24
    reminderType = "Screening"
    recipientNationality = "undefined"
} | ConvertTo-Json

try {
    $response5 = Invoke-RestMethod -Uri "$baseUrl/api/appointment/new" -Method POST -Headers $headers -Body $body5 -SkipCertificateCheck
    Write-Host "Unexpected Success: Should have failed validation" -ForegroundColor Yellow
    Write-Host "Response: $($response5 | ConvertTo-Json)" -ForegroundColor Gray
} catch {
    Write-Host "Expected Error: $($_.Exception.Message)" -ForegroundColor Green
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Gray
    Write-Host "Response: $($_.ErrorDetails.Message)" -ForegroundColor Gray
}

# Test Case 6: Validation Error - ProcedureCode too long
Write-Host "`nTest Case 6: Validation Error - ProcedureCode too long" -ForegroundColor Green
$body6 = @{
    appointmentIdentifier = "TEST006"
    appointmentType = "Breast Screening"
    procedureCode = "A" * 101  # 101 characters (exceeds 100 limit)
    appointmentLocation = "RAK"
    appointmentLocationDescription = "Rawdat Al Khail"
    appointmentDateTime = (Get-Date).AddDays(30).ToString("yyyy-MM-ddTHH:mm:ss")
    appointmentArriveMinutesBefore = 30
    customIdentifier = "HC00000006"
    subjectIdentifier = "HC00000006"
    recipientFirstName = "AAAAAAAAAAA"
    recipientLastName = "bbbbbbbb"
    recipientPhone = "12345678"
    recipientEmail = "<EMAIL>"
    sendDateTime = (Get-Date).AddDays(28).ToString("yyyy-MM-ddTHH:mm:ss")
    reminderSendHoursBefore = 24
    reminderType = "Screening"
    recipientNationality = "undefined"
} | ConvertTo-Json

try {
    $response6 = Invoke-RestMethod -Uri "$baseUrl/api/appointment/new" -Method POST -Headers $headers -Body $body6 -SkipCertificateCheck
    Write-Host "Unexpected Success: Should have failed validation" -ForegroundColor Yellow
    Write-Host "Response: $($response6 | ConvertTo-Json)" -ForegroundColor Gray
} catch {
    Write-Host "Expected Error: $($_.Exception.Message)" -ForegroundColor Green
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Gray
    Write-Host "Response: $($_.ErrorDetails.Message)" -ForegroundColor Gray
}

Write-Host "`nAll tests completed!" -ForegroundColor Cyan
